{% extends 'base.html' %}

{% block title %}Solar Generation Prediction | SolarPredict{% endblock title %}

{% block head_extras %}
    <!-- Leaflet -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <!-- Leaflet Geocoder -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.css" />
    <script src="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>
    <!-- jsPDF -->
    <script src="https://unpkg.com/jspdf@2.4.0/dist/jspdf.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
{% endblock head_extras %}

{% block extra_css %}
    .map-container {
        height: calc(100vh - 76px - 80px);
        display: flex;
        flex-wrap: wrap;
        overflow-y: auto;
    }
    
    .map-section {
        flex: 1 1 50%;
        min-width: 300px;
        height: 100%;
        position: relative;
    }
    
    .info-section {
        flex: 1 1 50%;
        min-width: 300px;
        padding: 20px;
        overflow-y: auto;
        background: var(--light-bg);
        border-left: 1px solid #dee2e6;
        max-height: calc(100vh - 76px - 80px);
    }
    
    #map {
        width: 100%;
        height: 100%;
        z-index: 1;
    }
    
    .coordinates-display {
        position: absolute;
        bottom: 10px;
        left: 10px;
        background: rgba(255,255,255,0.9);
        padding: 5px 10px;
        border-radius: 4px;
        z-index: 1000;
        font-size: 12px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .loading {
        display: none;
        margin: 20px 0;
        text-align: center;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--secondary-color);
        border-radius: 50%;
        animation: spin 2s linear infinite;
        margin: 0 auto;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .weather-icon {
        font-size: 2rem;
        color: var(--accent-color);
    }
    
    .weather-card {
        transition: all 0.3s ease;
    }
    
    .weather-card:hover {
        transform: translateY(-5px);
    }
    
    .btn-predict {
        background-color: var(--secondary-color);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-predict:hover {
        background-color: #219653;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .date-picker-container {
        background: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }
    
    .prediction-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .prediction-header i {
        font-size: 1.5rem;
        color: var(--secondary-color);
        margin-right: 10px;
    }
    
    .map-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        background: white;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .map-controls button {
        margin-bottom: 5px;
    }
    
    .location-marker {
        animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
    }
    
    .energy-stat {
        font-size: 2rem;
        font-weight: 600;
        color: var(--secondary-color);
    }
    
    .stat-card {
        text-align: center;
        padding: 15px;
        border-radius: 10px;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }
    
    .stat-card i {
        font-size: 2rem;
        color: var(--accent-color);
        margin-bottom: 10px;
    }
    
    .dashboard-summary {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .summary-card {
        flex: 1 1 calc(50% - 15px);
        min-width: 200px;
        background: white;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
    }
    
    .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.5rem;
    }
    
    .summary-icon.energy {
        background-color: rgba(39, 174, 96, 0.1);
        color: var(--secondary-color);
    }
    
    .summary-icon.weather {
        background-color: rgba(243, 156, 18, 0.1);
        color: var(--accent-color);
    }
    
    .summary-data h4 {
        font-size: 1.2rem;
        margin-bottom: 5px;
    }
    
    .summary-data p {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0;
    }
    
    .prediction-tabs {
        margin-top: 20px;
        border-radius: 10px;
        overflow: hidden;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .tab-buttons {
        display: flex;
        border-bottom: 1px solid #dee2e6;
    }
    
    .tab-button {
        flex: 1;
        padding: 12px;
        text-align: center;
        background: none;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .tab-button.active {
        color: var(--secondary-color);
        border-bottom: 2px solid var(--secondary-color);
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .tab-pane {
        display: none;
    }
    
    .tab-pane.active {
        display: block;
    }
    
    .footer {
        position: relative;
        z-index: 10;
    }
    
    .energy-trend-chart {
        height: 250px;
        margin-top: 15px;
        margin-bottom: 15px;
    }
    
    .prediction-actions {
        position: sticky;
        bottom: 0;
        background: white;
        padding: 15px;
        border-top: 1px solid #dee2e6;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
        z-index: 100;
        margin: 0 -20px;
        margin-top: 20px;
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .dashboard-view {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: #f8f9fa;
        z-index: 1100;
        overflow-y: auto;
        padding-top: 60px;
    }
    
    .dashboard-header {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: white;
        padding: 10px 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1200;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .dashboard-header h2 {
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }
    
    .dashboard-header h2 i {
        margin-right: 10px;
        color: var(--secondary-color);
    }
    
    .dashboard-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #6c757d;
        transition: all 0.3s ease;
    }
    
    .dashboard-close:hover {
        color: #dc3545;
    }
    
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .dashboard-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .dashboard-card-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .dashboard-card-header h3 {
        margin: 0;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
    }
    
    .dashboard-card-header h3 i {
        margin-right: 10px;
        color: var(--secondary-color);
    }
    
    .dashboard-card-body {
        padding: 20px;
    }
    
    .dashboard-card-large {
        grid-column: span 2;
    }
    
    .dashboard-stat {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .dashboard-stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.8rem;
    }
    
    .dashboard-stat-icon.energy {
        background-color: rgba(39, 174, 96, 0.1);
        color: var(--secondary-color);
    }
    
    .dashboard-stat-icon.weather {
        background-color: rgba(243, 156, 18, 0.1);
        color: var(--accent-color);
    }
    
    .dashboard-stat-icon.location {
        background-color: rgba(52, 152, 219, 0.1);
        color: #3498db;
    }
    
    .dashboard-stat-icon.date {
        background-color: rgba(155, 89, 182, 0.1);
        color: #9b59b6;
    }
    
    .dashboard-stat-data h4 {
        font-size: 1.5rem;
        margin-bottom: 5px;
        font-weight: 600;
    }
    
    .dashboard-stat-data p {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0;
    }
    
    .chart-container {
        height: 300px;
        margin-top: 15px;
    }
    
    .weather-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .weather-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
    }
    
    .weather-item i {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }
    
    .weather-item h5 {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }
    
    .weather-item p {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0;
    }
    
    .hourly-forecast {
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
        margin: 0 -10px;
    }
    
    .hourly-item {
        flex: 0 0 auto;
        width: 80px;
        text-align: center;
        padding: 10px;
    }
    
    .hourly-item p {
        margin-bottom: 5px;
    }
    
    .hourly-item i {
        font-size: 1.2rem;
        margin: 5px 0;
    }
    
    .hourly-item .temp {
        font-weight: 600;
    }
    
    .dashboard-footer {
        text-align: center;
        padding: 20px;
        color: #6c757d;
        border-top: 1px solid #eee;
        margin-top: 30px;
    }
    
    .dashboard-actions {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 30px;
    }
    
    .dashboard-btn {
        padding: 10px 25px;
        border-radius: 50px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .dashboard-btn i {
        margin-right: 8px;
    }
    
    .dashboard-btn-primary {
        background-color: var(--secondary-color);
        color: white;
        border: none;
    }
    
    .dashboard-btn-primary:hover {
        background-color: #219653;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .dashboard-btn-outline {
        background-color: transparent;
        color: var(--secondary-color);
        border: 1px solid var(--secondary-color);
    }
    
    .dashboard-btn-outline:hover {
        background-color: rgba(39, 174, 96, 0.1);
        transform: translateY(-2px);
    }
    
    /* Animation for dashboard transition */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    .dashboard-view.active {
        display: block;
        animation: fadeIn 0.5s ease forwards;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .dashboard-card-large {
            grid-column: span 1;
        }
    }
    
    /* Additional CSS to fix dashboard UI issues */
    .dashboard-view {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: #f8f9fa;
        z-index: 1100;
        overflow-y: auto;
        padding-top: 60px;
    }
    
    .dashboard-view.active {
        display: block;
    }
    
    .dashboard-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;
        margin-bottom: 30px;
    }
    
    .dashboard-btn {
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
    }
    
    .dashboard-btn i {
        margin-right: 8px;
    }
    
    .dashboard-btn-outline {
        background: transparent;
        border: 1px solid var(--secondary-color);
        color: var(--secondary-color);
    }
    
    .dashboard-btn-outline:hover {
        background: rgba(39, 174, 96, 0.1);
    }
    
    .dashboard-btn-primary {
        background: var(--secondary-color);
        color: white;
    }
    
    .dashboard-btn-primary:hover {
        background: #219653;
        transform: translateY(-2px);
    }
    
    .dashboard-footer {
        text-align: center;
        color: #6c757d;
        padding: 20px 0;
        border-top: 1px solid #eee;
    }
    
    .hourly-forecast {
        display: flex;
        overflow-x: auto;
        gap: 10px;
        padding-bottom: 10px;
    }
    
    .hourly-item {
        min-width: 80px;
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .hourly-item p {
        margin-bottom: 5px;
    }
    
    .hourly-item .time {
        font-weight: 600;
    }
    
    .hourly-item .energy {
        color: var(--secondary-color);
        font-weight: 600;
    }
    
    /* Chart zoom styling */
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
        overflow: hidden;
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .chart-container:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    #detailedEnergyChart {
        transition: cursor 0.2s ease;
    }
    
    .zoom-help {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        align-items: center;
    }
    
    .zoom-help i {
        margin-right: 5px;
    }
{% endblock extra_css %}



{% block content %}
<div class="map-container">
    <div class="map-section">
        <div id="map"></div>
        <div class="coordinates-display" id="coordinates">
            <i class="fas fa-map-marker-alt me-1"></i> Click on map to select location
        </div>
        <div class="map-controls">
            <button class="btn btn-sm btn-light mb-2" id="zoomIn"><i class="fas fa-search-plus"></i></button>
            <button class="btn btn-sm btn-light mb-2" id="zoomOut"><i class="fas fa-search-minus"></i></button>
            <button class="btn btn-sm btn-light" id="resetView"><i class="fas fa-globe-americas"></i></button>
        </div>
    </div>
    
    <div class="info-section">
        <div class="prediction-header">
            <i class="fas fa-solar-panel"></i>
            <h3>Solar Generation Prediction</h3>
        </div>

        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> Select a location on the map and a date to predict solar energy generation.
        </div>

        <div class="dashboard-summary" id="dashboard-summary">
            <div class="summary-card">
                <div class="summary-icon energy">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="summary-data">
                    <h4 id="today-energy">-- kWh</h4>
                    <p>Today's Prediction</p>
                </div>
            </div>
            <div class="summary-card">
                <div class="summary-icon weather">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="summary-data">
                    <h4 id="today-condition">--</h4>
                    <p>Weather Condition</p>
                </div>
            </div>
        </div>

        <div class="card mb-4 weather-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-cloud-sun me-2"></i>Location Weather</span>
                <span class="badge bg-primary" id="location-badge">Not Selected</span>
            </div>
            <div class="card-body" id="weather">
                <div class="text-center py-4">
                    <div class="weather-icon mb-3">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <p class="text-muted">Click on the map to select a location and view weather conditions.</p>
                </div>
            </div>
        </div>
        
        <!-- <div class="prediction-tabs">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="daily">Daily Forecast</button>
                <button class="tab-button" data-tab="weekly">Weekly Trend</button>
            </div>
            <div class="tab-content">
                <div class="tab-pane active" id="daily-tab">
                    <div class="energy-trend-chart" id="daily-chart">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-chart-line mb-3" style="font-size: 2rem;"></i>
                            <p>Select a location to view daily energy production forecast</p>
                        </div>
                    </div>
                </div>
                <div class="tab-pane" id="weekly-tab">
                    <div class="energy-trend-chart" id="weekly-chart">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-chart-bar mb-3" style="font-size: 2rem;"></i>
                            <p>Select a location to view weekly energy production trend</p>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->
        
        <div class="prediction-actions">
            {% csrf_token %}
            <div class="date-picker-container mb-3">
                <label for="dateInput" class="form-label"><i class="far fa-calendar-alt me-2"></i>Select Date for Prediction:</label>
                <input type="date" id="dateInput" class="form-control">
            </div>
            <button class="btn btn-predict w-100" onclick="confirmLocationAndPredict()">
                <i class="fas fa-bolt me-2"></i>Generate Prediction
            </button>
        </div>

        <div class="loading mt-4" id="loading" style="display: none;">
            <div class="loading-spinner mb-3"></div>
            <p>Analyzing solar potential and generating prediction...</p>
        </div>

        <div id="prediction-results" class="mt-4"></div>
    </div>
</div>
<!-- Add dashboard view -->
<div id="dashboard-view" class="dashboard-view">
    <div class="dashboard-header">
        <h2><i class="fas fa-solar-panel"></i> Solar Generation Dashboard</h2>
        <button id="close-dashboard" class="dashboard-close"><i class="fas fa-times"></i></button>
    </div>
    
    <div class="dashboard-container">
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h3><i class="fas fa-info-circle"></i> Prediction Summary</h3>
                    <span id="dashboard-date" class="badge bg-primary">Date</span>
                </div>
                <div class="dashboard-card-body">
                    <div class="dashboard-stat">
                        <div class="dashboard-stat-icon energy">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="dashboard-stat-data">
                            <h4 id="dashboard-energy">-- kWh</h4>
                            <p>Total Energy Generated</p>
                        </div>
                    </div>
                    
                    <div class="dashboard-stat">
                        <div class="dashboard-stat-icon weather">
                            <i class="fas fa-sun"></i>
                        </div>
                        <div class="dashboard-stat-data">
                            <h4 id="dashboard-condition">--</h4>
                            <p>Weather Condition</p>
                        </div>
                    </div>
                    
                    <div class="dashboard-stat">
                        <div class="dashboard-stat-icon location">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="dashboard-stat-data">
                            <h4 id="dashboard-location">--</h4>
                            <p>Selected Location</p>
                        </div>
                        
                    </div>
                    
                    <div class="dashboard-stat">
                        <div class="dashboard-stat-icon date">
                            <i class="far fa-calendar-alt"></i>
                        </div>
                        <div class="dashboard-stat-data">
                            <h4 id="dashboard-date-full">--</h4>
                            <p>Prediction Date</p>
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h3><i class="fas fa-cloud-sun"></i> Weather Conditions</h3>
                </div>
                <div class="dashboard-card-body">
                    <div id="dashboard-weather-icon" class="text-center mb-3">
                        <i class="fas fa-sun" style="font-size: 3rem; color: #f39c12;"></i>
                    </div>
                    
                    <div class="weather-grid">
                        <div class="weather-item">
                            <i class="fas fa-temperature-high" style="color: #e74c3c;"></i>
                            <h5>Temperature</h5>
                            <p id="dashboard-temp">-- °C</p>
                        </div>
                        <div class="weather-item">
                            <i class="fas fa-tint" style="color: #3498db;"></i>
                            <h5>Humidity</h5>
                            <p id="dashboard-humidity">--%</p>
                        </div>
                        <div class="weather-item">
                            <i class="fas fa-sun" style="color: #f39c12;"></i>
                            <h5>Solar Radiation</h5>
                            <p id="dashboard-radiation">-- W/m²</p>
                        </div>
                        <div class="weather-item">
                            <i class="fas fa-cloud" style="color: #95a5a6;"></i>
                            <h5>Cloud Cover</h5>
                            <p id="dashboard-clouds">--%</p>
                        </div>
                    </div>
                    
                    
                </div>
            </div>
            
            <div class="dashboard-card dashboard-card-large">
                <div class="dashboard-card-header">
                    <h3><i class="fas fa-chart-line"></i> Energy Production Forecast</h3>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        <canvas id="dashboard-energy-chart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card dashboard-card-large">
                <div class="dashboard-card-header">
                    <h3><i class="fas fa-chart-bar"></i> Weekly Energy Trend</h3>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        <canvas id="dashboard-weekly-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="dashboard-card mt-4">
            <div class="dashboard-card-header">
                <h3><i class="fas fa-chart-area"></i> Detailed Energy Production</h3>
            </div>
            <div class="dashboard-card-body">
                <div id="dashboard-plot-container">
                    <!-- Plot will be inserted here -->
                </div>
            </div>
        </div>
        
        <div class="dashboard-actions">
            <button class="dashboard-btn dashboard-btn-outline" id="export-report">
                <i class="fas fa-file-export"></i> Export Report
            </button>
            <button class="dashboard-btn dashboard-btn-outline" id="register-rts">
                <i class="fas fa-solar-panel"></i> Register RTS
            </button>
            <button class="dashboard-btn dashboard-btn-primary" id="new-prediction">
                <i class="fas fa-plus"></i> New Prediction
            </button>
        </div>
        
        <div class="dashboard-footer">
            <p>© 2025 SolarPredict | Advanced Solar Generation Prediction Platform</p>
        </div>
    </div>
</div>
{% endblock content %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.0"></script>
<script>
    // Initialize map
    const map = L.map('map').setView([20, 0], 2);
    const coordDisplay = document.getElementById('coordinates');

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
    }).addTo(map);

    // Add geocoder
    const geocoder = L.Control.geocoder({
        defaultMarkGeocode: false,
        placeholder: 'Search for a location...',
        errorMessage: 'Nothing found.',
        showResultIcons: true
    })
    .on('markgeocode', function(e) {
        const latlng = e.geocode.center;
        placeMarkerAndFetchWeather(latlng);
        map.setView(latlng, 10);
    })
    .addTo(map);

    // Map click event
    map.on('click', function(e) {
        placeMarkerAndFetchWeather(e.latlng);
    });
    
    // Map mousemove event
    map.on('mousemove', function(e) {
        coordDisplay.innerHTML = `<i class="fas fa-map-marker-alt me-1"></i> Lat: ${e.latlng.lat.toFixed(4)}, Lng: ${e.latlng.lng.toFixed(4)}`;
    });
    
    // Map control buttons
    document.getElementById('zoomIn').addEventListener('click', function() {
        map.zoomIn();
    });
    
    document.getElementById('zoomOut').addEventListener('click', function() {
        map.zoomOut();
    });
    
    document.getElementById('resetView').addEventListener('click', function() {
        map.setView([20, 0], 2);
    });
    
    // Tab functionality
    document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and panes
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
            
            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            document.getElementById(`${this.dataset.tab}-tab`).classList.add('active');
        });
    });
    
    // Variables
    let marker;
    let selectedLatLng = null;
    let dailyChart = null;
    let weeklyChart = null;
    let prediction = null;
    let latestWeatherData = null;

    

    // Place marker and fetch weather
    function placeMarkerAndFetchWeather(latlng) {
        // Remove existing marker if any
        if (marker) {
            map.removeLayer(marker);
        }
        
        // Custom marker icon with pulse effect
        const markerIcon = L.divIcon({
            html: '<div class="location-marker"><i class="fas fa-map-marker-alt" style="color: #27ae60; font-size: 24px;"></i></div>',
            className: '',
            iconSize: [24, 24],
            iconAnchor: [12, 24]
        });
        
        marker = L.marker(latlng, {icon: markerIcon}).addTo(map);
        selectedLatLng = latlng;
        
        // Update location badge
        document.getElementById('location-badge').textContent = `${latlng.lat.toFixed(4)}, ${latlng.lng.toFixed(4)}`;
        
        // Show loading in weather card
        document.getElementById('weather').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-2">Fetching weather data...</p>
            </div>
        `;

        fetch(`/get_weather/?lat=${latlng.lat}&lon=${latlng.lng}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('weather').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> Error: ${data.error}
                        </div>
                    `;
                } else {
                    latestWeatherData = data;
                    // Get weather icon based on cloud cover
                    let weatherIcon = 'fa-sun';
                    let weatherColor = '#f39c12';
                    let weatherCondition = 'Sunny';
                    const cloudCover = data.weather.cloud_cover;
                    if (data.weather.is_day === false) {
                        weatherIcon = 'fa-moon';
                        weatherColor = '#2c3e50';
                        weatherCondition = 'Night';
                    } 
                    else {
                        if (cloudCover > 80) {
                            weatherIcon = 'fa-cloud';
                            weatherColor = '#7f8c8d';
                            weatherCondition = 'Cloudy';
                        } else if (cloudCover > 50) {
                            weatherIcon = 'fa-cloud-sun';
                            weatherColor = '#95a5a6';
                            weatherCondition = 'Partly Cloudy';
                        } else if (cloudCover > 20) {
                            weatherIcon = 'fa-sun';
                            weatherColor = '#f39c12';
                            weatherCondition = 'Mostly Sunny';
                        }
                    }
                    
                    // // Update today's condition in dashboard
                    // document.getElementById('today-condition').textContent = weatherCondition;
                    
                    // Display weather data
                    document.getElementById('weather').innerHTML = `
                        <div class="text-center mb-3">
                            <i class="fas ${weatherIcon}" style="font-size: 3rem; color: ${weatherColor};"></i>
                        </div>
                        <h5 class="text-center mb-4">Weather at ${latlng.lat.toFixed(4)}, ${latlng.lng.toFixed(4)}</h5>
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <i class="fas fa-temperature-high mb-2" style="color: #e74c3c;"></i>
                                    <h6>Temperature</h6>
                                    <p class="mb-0 fw-bold">${data.weather.temperature} °C</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <i class="fas fa-tint mb-2" style="color: #3498db;"></i>
                                    <h6>Humidity</h6>
                                    <p class="mb-0 fw-bold">${data.weather.humidity}%</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <i class="fas fa-sun mb-2" style="color: #f39c12;"></i>
                                    <h6>Solar Radiation</h6>
                                    <p class="mb-0 fw-bold">${data.weather.radiation} W/m²</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-light rounded text-center">
                                    <i class="fas fa-cloud mb-2" style="color: #95a5a6;"></i>
                                    <h6>Cloud Cover</h6>
                                    <p class="mb-0 fw-bold">${data.weather.cloud_cover}%</p>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // // Generate today's prediction automatically
                    // generateTodayPrediction(data);
                    // console.log("Weather API data received:", data);

                    // // Create charts
                    // createDailyChart(data);
                    // // createWeeklyChart(data);
                }
            })
            .catch(err => {
                document.getElementById('weather').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> Error fetching weather data
                    </div>
                `;
                console.error(err);
            });
    }
    
// function pollPredictionResults() {
//     let retries = 0;
//     const maxRetries = 60;
//     console.log("Starting prediction polling...");

//     function poll() {
//         console.log(`Polling attempt ${retries+1}/${maxRetries}`);
//         fetch('/get_prediction_results/')
//         .then(response => {
//             console.log("Poll response status:", response.status);
//             return response.json();
//         })
//         .then(data => {
//             console.log("Poll data received:", data);
//             if (data.status === 'ready') {
//                 document.getElementById('loading').style.display = 'none';
//                 displayPredictionResults(data.prediction);
//             } else if (data.status === 'pending') {
//                 if (retries < maxRetries) {
//                     retries++;
//                     setTimeout(poll, 2000); // try again after 2 seconds
//                 } else {
//                     document.getElementById('loading').style.display = 'none';
//                     Swal.fire({
//                         icon: 'error',
//                         title: 'Timeout',
//                         text: 'Prediction took too long. Please try again later.',
//                         confirmButtonColor: '#27ae60'
//                     });
//                 }
//             } else if (data.error) {
//                 document.getElementById('loading').style.display = 'none';
//                 Swal.fire({
//                     icon: 'error',
//                     title: 'Polling Error',
//                     text: data.error,
//                     confirmButtonColor: '#27ae60'
//                 });
//             }
//         })
//         .catch(err => {
//             document.getElementById('loading').style.display = 'none';
//             Swal.fire({
//                 icon: 'error',
//                 title: 'Polling Failed',
//                 text: `Error fetching results: ${err.message}`,
//                 confirmButtonColor: '#27ae60'
//             });
//             console.error(err);
//         });
//     }

//     poll();
// }

function displayPredictionResults(prediction) {
    console.log("Displaying prediction results:", prediction);
    
    // Format date for display
    const selectedDate = new Date(document.getElementById('dateInput').value);
    const displayDate = selectedDate.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
    
    // Update dashboard with prediction results
    document.getElementById('today-energy').textContent = `${prediction.total_energy} kWh`;
    document.getElementById('dashboard-energy').textContent = `${prediction.total_energy} kWh`;
    document.getElementById('dashboard-date').textContent = displayDate;
    document.getElementById('dashboard-date-full').textContent = displayDate;
    
    const selectedLatLng = window.selectedLocation || { lat: 0, lng: 0 };
    document.getElementById('dashboard-location').textContent = `${selectedLatLng.lat.toFixed(4)}, ${selectedLatLng.lng.toFixed(4)}`;

    // Update weather details if included in prediction
    if (prediction.weather) {
        // The condition property doesn't exist in your weather object
        // Let's determine the condition based on cloud_cover and is_day
        let condition = "Sunny";
        if (prediction.weather.cloud_cover > 80) {
            condition = "Cloudy";
        } else if (prediction.weather.cloud_cover > 50) {
            condition = "Partly Cloudy";
        } else if (prediction.weather.cloud_cover > 20) {
            condition = "Mostly Sunny";
        }
        
        // If it's night, adjust the condition
        if (prediction.weather.is_day === 0) {
            if (condition === "Sunny") {
                condition = "Clear";
            } else if (condition === "Mostly Sunny") {
                condition = "Partly Clear";
            }
        }
        
        document.getElementById('today-condition').textContent = condition;
        document.getElementById('dashboard-condition').textContent = condition;
        document.getElementById('dashboard-temp').textContent = `${prediction.weather.temperature} °C`;
        document.getElementById('dashboard-humidity').textContent = `${prediction.weather.humidity}%`;
        document.getElementById('dashboard-radiation').textContent = `${prediction.weather.radiation} W/m²`;
        document.getElementById('dashboard-clouds').textContent = `${prediction.weather.cloud_cover}%`;

        let weatherIcon = 'fa-sun';
        let weatherColor = '#f39c12';
        
        // Use our determined condition instead of accessing prediction.weather.condition
        if (condition.toLowerCase().includes('cloud')) {
            weatherIcon = 'fa-cloud-sun';
            weatherColor = '#95a5a6';
        } else if (condition.toLowerCase().includes('rain')) {
            weatherIcon = 'fa-cloud-showers-heavy';
            weatherColor = '#3498db';
        } else if (condition.toLowerCase() === 'clear') {
            weatherIcon = 'fa-moon';
            weatherColor = '#34495e';
        }
        
        document.getElementById('dashboard-weather-icon').innerHTML = `
            <i class="fas ${weatherIcon}" style="font-size: 3rem; color: ${weatherColor};"></i>
        `;
    } else {
        console.warn("No weather details returned in prediction results.");
    }

    // Show detailed prediction plot if available
    if (prediction.forecast_energy) {
        const hourly = prediction.forecast_energy;
        
        // Create formatted datetime labels that include both date and time
        const times = [];
        if (hourly.hours && hourly.hours.length) {
            // Get the selected date
            const selectedDate = document.getElementById('dateInput').value;
            
            // Create datetime strings for each hour
            hourly.hours.forEach((timeStr, index) => {
                // For simplicity, assume each consecutive 24 hours is a new day
                const dayOffset = Math.floor(index / 24);
                const date = new Date(selectedDate);
                date.setDate(date.getDate() + dayOffset);
                
                // Format as YYYY-MM-DD HH:MM
                const dateStr = date.toISOString().split('T')[0];
                times.push(`${dateStr} ${timeStr}`);
            });
        } else {
            console.warn("No valid hours data found.");
        }
        
        const energy = hourly.predicted_energy || [];
        const radiation = hourly.GHI || [];
        
        if (!times.length || !energy.length) {
            console.warn("No valid hourly prediction data found.");
            return;
        }
        
        // Use all available data for detailed chart (no filtering)
        const ctxContainer = document.getElementById('dashboard-plot-container');
        ctxContainer.innerHTML = `
            <div class="chart-controls mb-3 d-flex align-items-center">
                <div class="zoom-help me-auto">
                    <small class="text-muted"><i class="fas fa-info-circle me-1"></i>Use mouse wheel to zoom in/out</small>
                </div>
                <div class="btn-group">
                    <button id="zoom-in" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button id="zoom-out" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button id="reset-zoom" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
            </div>
            <div class="chart-container" style="position: relative; height: 400px; background-color: rgba(255, 255, 255, 0.8); border-radius: 10px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); padding: 15px;">
                <canvas id="detailedEnergyChart"></canvas>
            </div>
        `; // Clear old & add canvas with controls in a fixed-height container
        
        if (window.detailedChartInstance) {
            window.detailedChartInstance.destroy();
        }
        
        window.detailedChartInstance = new Chart(document.getElementById('detailedEnergyChart'), {
            type: 'line',
            data: {
                labels: times,
                datasets: [
                    {
                        label: 'Predicted Energy (kWh)',
                        data: energy,
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.15)',
                        fill: true,
                        tension: 0.3,
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHoverBackgroundColor: '#27ae60',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2
                    },
                    ...(radiation.length ? [{
                        label: 'Solar Radiation (W/m²)',
                        data: radiation,
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.05)',
                        borderDash: [5, 5],
                        fill: false,
                        tension: 0.3,
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHoverBackgroundColor: '#f39c12',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2,
                        yAxisID: 'y1'
                    }] : [])
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                plugins: {
                    legend: { 
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'Detailed Energy Production Forecast',
                        font: { 
                            size: 16,
                            weight: 'bold' 
                        },
                        padding: {
                            top: 10,
                            bottom: 20
                        }
                    },
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'x',
                            threshold: 5
                        },
                        zoom: {
                            wheel: { 
                                enabled: true,
                                speed: 0.05
                            },
                            pinch: { 
                                enabled: true 
                            },
                            mode: 'x' // Only zoom on x-axis (time)
                        },
                        limits: {
                            x: {
                                min: 'original', // Don't allow zooming beyond the original data range
                                max: 'original',
                                minRange: 4 // Minimum 4 time points visible
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        cornerRadius: 6,
                        padding: 10,
                        boxPadding: 5,
                        usePointStyle: true,
                        callbacks: {
                            title: function(tooltipItems) {
                                const parts = tooltipItems[0].label.split(' ');
                                const date = parts[0];
                                const time = parts[1];
                                return `${date} at ${time}`;
                            },
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(2);
                                    if (context.dataset.label === 'Predicted Energy (kWh)') {
                                        label += ' kWh';
                                    } else if (context.dataset.label === 'Solar Radiation (W/m²)') {
                                        label += ' W/m²';
                                    }
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { 
                            display: true, 
                            text: 'Energy (kWh)',
                            font: {
                                weight: 'bold',
                                size: 12
                            },
                            padding: {top: 0, bottom: 10}
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 8,
                            font: {
                                size: 11
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: { 
                            drawOnChartArea: false,
                            drawBorder: false
                        },
                        title: { 
                            display: true, 
                            text: 'Solar Radiation (W/m²)',
                            font: {
                                weight: 'bold',
                                size: 12
                            },
                            padding: {top: 0, bottom: 10}
                        },
                        ticks: {
                            padding: 8,
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        title: { 
                            display: true, 
                            text: 'Date/Time',
                            font: {
                                weight: 'bold',
                                size: 12
                            },
                            padding: {top: 10, bottom: 0}
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 8,
                            maxRotation: 0,
                            autoSkip: true,
                            font: {
                                size: 11
                            },
                            callback: function(value, index, values) {
                                const timeStr = this.getLabelForValue(value);
                                const parts = timeStr.split(' ');
                                if (parts.length < 2) return timeStr;
                                
                                const timePart = parts[1];
                                const datePart = parts[0];
                                const hour = parseInt(timePart.split(':')[0]);
                                
                                // Calculate visible range
                                const visibleDataPoints = values.length;
                                
                                // If zoomed in (less than 48 data points visible), show more time details
                                if (visibleDataPoints < 48) {
                                    // Show time every 4 hours
                                    if (hour % 4 === 0) {
                                        return hour === 0 ? `${datePart}` : `${hour}:00`;
                                    }
                                    return '';
                                } else if (visibleDataPoints < 96) {
                                    // Medium zoom level - show only 00:00 and 12:00
                                    if (hour === 0) {
                                        return datePart;
                                    } else if (hour === 12) {
                                        return '12:00';
                                    }
                                    return '';
                                } else {
                                    // When zoomed out, only show dates at midnight
                                    return hour === 0 ? datePart : '';
                                }
                            }
                        }
                    }
                }
            }
        });
        
        // Add event listeners for zoom control buttons with smoother zooming
        document.getElementById('zoom-in').addEventListener('click', function() {
            window.detailedChartInstance.zoom(1.1);
        });

        document.getElementById('zoom-out').addEventListener('click', function() {
            window.detailedChartInstance.zoom(0.9);
        });

        document.getElementById('reset-zoom').addEventListener('click', function() {
            window.detailedChartInstance.resetZoom();
        });

        // Add a visual indicator when zooming
        const chartCanvas = document.getElementById('detailedEnergyChart');
        chartCanvas.addEventListener('wheel', function(e) {
            if (e.deltaY < 0) {
                chartCanvas.style.cursor = 'zoom-in';
            } else {
                chartCanvas.style.cursor = 'zoom-out';
            }
            
            // Reset cursor after a short delay
            setTimeout(() => {
                chartCanvas.style.cursor = 'default';
            }, 300);
        });
    }


    // Update hourly energy chart
    if (prediction.forecast_energy) {
        createDashboardEnergyChart(prediction.forecast_energy);
    } else if (prediction.hourly_energy) {
        createDashboardEnergyChart(prediction.hourly_energy);
    } else {
        console.warn("No hourly energy data found for chart.");
    }

    // Update weekly (daily energy) chart
    if (prediction.daily_energy) {
        createDashboardWeeklyChart(prediction.daily_energy);
    } else {
        console.warn("No daily energy data found for weekly chart.");
    }

    // Show the dashboard
    document.getElementById('dashboard-view').classList.add('active');
}


function renderDashboardCharts(prediction) {
    createDashboardEnergyChart(prediction.forecast_energy);
    createDashboardWeeklyChart(prediction.daily_energy);

    // You can also update any summary cards here with prediction.total_energy, etc.
}


    // Generate today's prediction
    function generateTodayPrediction(data) {
        // Simple estimation based on solar radiation and cloud cover
        const solarRadiation = data.hourly.shortwave_radiation_instant.slice(0, 24);
        const cloudCover = data.hourly.cloud_cover.slice(0, 24);
        
        // Calculate estimated energy production (simplified model)
        let totalEnergy = 0;
        for (let i = 0; i < 24; i++) {
            // Simple formula: radiation * (1 - cloudCover/100) * efficiency
            const hourlyEnergy = solarRadiation[i] * (1 - cloudCover[i]/100) * 0.15 * 0.001;
            totalEnergy += hourlyEnergy;
        }
        
        // Update dashboard with today's prediction
        document.getElementById('today-energy').textContent = `${totalEnergy.toFixed(2)} kWh`;
    }
    
    // Create daily chart
    function createDailyChart(data) {
    console.log("Creating daily chart with data:", data);

    if (!data.hourly || !data.hourly.Time) {
        console.error("Incomplete data for daily chart:", data);
        return;
    }

    const currentDate = data.current_date; // date in YYYY-MM-DD from backend
    const dailyTimes = [];
    const dailyRadiation = [];
    const dailyClouds = [];

    for (let i = 0; i < data.hourly.Time.length; i++) {
        const timestamp = new Date(data.hourly.Time[i]);
        const dateStr = timestamp.toISOString().split("T")[0];

        if (dateStr === currentDate) {
            dailyTimes.push(timestamp.getHours());
            dailyRadiation.push(data.hourly.shortwave_radiation_instant[i]);
            dailyClouds.push(data.hourly.cloud_cover[i]);
        }
    }

    if (dailyTimes.length === 0) {
        console.error("No data for current date:", currentDate);
        Swal.fire({
            icon: 'error',
            title: 'No Data',
            text: 'No hourly forecast data found for today.',
            confirmButtonColor: '#27ae60'
        });
        return;
    }

    const dailyEnergy = dailyRadiation.map((rad, i) => rad * (1 - dailyClouds[i] / 100) * 0.15 * 0.001);

    const ctxContainer = document.getElementById('daily-chart');
    ctxContainer.innerHTML = '<canvas id="dailyChartCanvas"></canvas>';

    if (window.dailyChartInstance) window.dailyChartInstance.destroy();

    window.dailyChartInstance = new Chart(document.getElementById('dailyChartCanvas'), {
        type: 'line',
        data: {
            labels: dailyTimes.map(h => `${h}:00`),
            datasets: [
                {
                    label: 'Energy Production (kWh)',
                    data: dailyEnergy,
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Solar Radiation (W/m²)',
                    data: dailyRadiation,
                    borderColor: '#f39c12',
                    borderDash: [5, 5],
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { position: 'top' }},
            scales: {
                y: { beginAtZero: true, title: { display: true, text: 'Energy (kWh)' }},
                y1: {
                    beginAtZero: true,
                    position: 'right',
                    grid: { drawOnChartArea: false },
                    title: { display: true, text: 'Solar Radiation (W/m²)' }
                },
                x: { title: { display: true, text: 'Time (Hour)' }}
            }
        }
    });
}


    // Create weekly chart
   function createWeeklyChartFromPrediction(prediction) {
    const ctxContainer = document.getElementById('weekly-chart');
    ctxContainer.innerHTML = '<canvas id="weeklyChartCanvas"></canvas>'; // clear & add canvas

    if (!prediction.daily_energy || !prediction.daily_energy.dates || !prediction.daily_energy.total_energy) {
        console.error("No valid daily_energy in prediction result:", prediction.daily_energy);
        ctxContainer.innerHTML = `<div class="text-center text-danger">No predicted daily energy data available</div>`;
        return;
    }

    const dates = prediction.daily_energy.dates;
    const totalEnergy = prediction.daily_energy.total_energy;

    if (!dates.length || !totalEnergy.length) {
        console.error("Empty daily_energy arrays in prediction result:", prediction.daily_energy);
        ctxContainer.innerHTML = `<div class="text-center text-danger">No predicted daily energy data found</div>`;
        return;
    }

    if (window.weeklyChartInstance) window.weeklyChartInstance.destroy();

    window.weeklyChartInstance = new Chart(document.getElementById('weeklyChartCanvas'), {
        type: 'bar',
        data: {
            labels: dates,
            datasets: [
                {
                    label: 'Predicted Daily Energy (kWh)',
                    data: totalEnergy,
                    backgroundColor: 'rgba(39, 174, 96, 0.7)',
                    borderColor: '#27ae60',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: { display: true, text: 'Energy (kWh)' }
                },
                x: {
                    title: { display: true, text: 'Date' }
                }
            }
        }
    });
}


    // Confirm location and predict
// function confirmLocationAndPredict() {
//     const date = document.getElementById('dateInput').value;
//     if (!date) {
//         Swal.fire({
//             icon: 'warning',
//             title: 'Date Required',
//             text: 'Please select a date for the prediction.',
//             confirmButtonColor: '#27ae60'
//         });
//         return;
//     }
    
//     if (!selectedLatLng) {
//         Swal.fire({
//             icon: 'warning',
//             title: 'Location Required',
//             text: 'Please select a location on the map first.',
//             confirmButtonColor: '#27ae60'
//         });
//         return;
//     }

//     // Show loader & clear old results
//     document.getElementById('loading').style.display = 'block';
//     document.getElementById('prediction-results').innerHTML = '';

//     const formData = new FormData();
//     formData.append('lat', selectedLatLng.lat);
//     formData.append('lon', selectedLatLng.lng);
//     formData.append('date', date);

//     fetch('/confirm_location_and_predict/', {
//         method: 'POST',
//         body: formData,
//         headers: { "X-CSRFToken": "{{ csrf_token }}" },
//     })
//     .then(response => response.json())
//     .then(data => {
//         // Always hide loader on response
//         document.getElementById('loading').style.display = 'none';

//         if (data.error) {
//             // Prediction failed
//             Swal.fire({
//                 icon: 'error',
//                 title: 'Prediction Failed',
//                 text: data.error,
//                 confirmButtonColor: '#27ae60'
//             });
//         } else {
//             // Update prediction results card directly
//             const displayDate = new Date(date).toLocaleDateString('en-US', {
//                 weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'
//             });

//             document.getElementById('prediction-results').innerHTML = `
//                 <div class="card mt-4">
//                     <div class="card-header">
//                         <i class="fas fa-chart-line me-2"></i> Prediction Results
//                     </div>
//                     <div class="card-body">
//                         <h5 class="card-title mb-4">Solar Generation for ${displayDate}</h5>
                        
//                         <div class="stat-card mb-4">
//                             <i class="fas fa-bolt"></i>
//                             <h3 class="energy-stat">${data.total_energy} kWh</h3>
//                             <p class="text-muted mb-0">Total Energy Generated</p>
//                         </div>
                        
//                         <div class="text-center mt-4">
//                             <iframe src="/${data.plot_path}" width="100%" height="500px" frameborder="0" class="rounded"></iframe>
//                         </div>
                        
//                         <div class="alert alert-success mt-4">
//                             <i class="fas fa-info-circle me-2"></i> This prediction is based on weather forecasts and historical solar data. Actual generation may vary based on real-time conditions.
//                         </div>
//                     </div>
//                 </div>
//             `;

//             // Optionally switch to the dashboard view if needed:
//             // document.getElementById('dashboard-view').classList.add('active');

//             Swal.fire({
//                 icon: 'success',
//                 title: 'Prediction Ready!',
//                 text: `Total predicted energy: ${data.total_energy} kWh for ${displayDate}.`,
//                 confirmButtonColor: '#27ae60'
//             });
//         }
//     })
//     .catch(err => {
//         document.getElementById('loading').style.display = 'none';
//         console.error(err);
//         Swal.fire({
//             icon: 'error',
//             title: 'Prediction Error',
//             text: `An unexpected error occurred: ${err.message}`,
//             confirmButtonColor: '#27ae60'
//         });
//     });
// }

    // Set default date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    document.getElementById('dateInput').valueAsDate = tomorrow;

    // Dashboard functionality
    const dashboardView = document.getElementById('dashboard-view');
    const closeDashboard = document.getElementById('close-dashboard');
    const newPrediction = document.getElementById('new-prediction');
    const exportReport = document.getElementById('export-report');
    const registerRTS = document.getElementById('register-rts');

    // Close dashboard and return to map
    closeDashboard.addEventListener('click', function() {
        dashboardView.classList.remove('active');
    });

    // New prediction button
    newPrediction.addEventListener('click', function() {
        dashboardView.classList.remove('active');
    });

    // Register RTS button
    registerRTS.addEventListener('click', function() {
        // Check if we have a selected location
        if (!selectedLatLng && !window.selectedLocation) {
            Swal.fire({
                icon: 'warning',
                title: 'No Location Selected',
                text: 'Please select a location on the map first.',
                confirmButtonColor: '#27ae60'
            });
            return;
        }
        
        // Use either selectedLatLng or window.selectedLocation
        const location = selectedLatLng || window.selectedLocation;
        
        Swal.fire({
            title: 'Register New RTS',
            html: `
                <form id="rts-form" class="text-start">
                    <div class="mb-3">
                        <label for="rts-name" class="form-label">RTS Name</label>
                        <input type="text" class="form-control" id="rts-name" placeholder="Enter RTS name">
                    </div>
                    <div class="mb-3">
                        <label for="rts-capacity" class="form-label">Capacity (kW)</label>
                        <input type="number" class="form-control" id="rts-capacity" placeholder="Enter capacity">
                    </div>
                    <div class="mb-3">
                        <label for="rts-location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="rts-location" value="${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="rts-owner" class="form-label">Owner</label>
                        <input type="text" class="form-control" id="rts-owner" placeholder="Enter owner name">
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Register',
            confirmButtonColor: '#27ae60',
            cancelButtonText: 'Cancel',
            focusConfirm: false,
            preConfirm: () => {
                const name = document.getElementById('rts-name').value;
                const capacity = document.getElementById('rts-capacity').value;
                const owner = document.getElementById('rts-owner').value;
                
                if (!name || !capacity || !owner) {
                    Swal.showValidationMessage('Please fill all fields');
                    return false;
                }
                
                return {
                    name: name,
                    capacity: capacity,
                    location: document.getElementById('rts-location').value,
                    owner: owner
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Here you would typically send this data to the server
                // For now, we'll just show a success message
                
                // Create a mock registration object
                const registration = {
                    id: generateUniqueId(),
                    name: result.value.name,
                    capacity: result.value.capacity,
                    location: result.value.location,
                    owner: result.value.owner,
                    date: new Date().toISOString()
                };
                
                // Store in localStorage for persistence
                saveRTSRegistration(registration);
                
                Swal.fire({
                    title: 'RTS Registered!',
                    text: `${result.value.name} has been successfully registered.`,
                    icon: 'success',
                    confirmButtonColor: '#27ae60'
                });
            }
        });
    });

    // Helper function to generate a unique ID
    function generateUniqueId() {
        return 'rts-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
    }

    // Helper function to save RTS registration to localStorage
    function saveRTSRegistration(registration) {
        // Get existing registrations or initialize empty array
        let registrations = JSON.parse(localStorage.getItem('rtsRegistrations') || '[]');
        
        // Add new registration
        registrations.push(registration);
        
        // Save back to localStorage
        localStorage.setItem('rtsRegistrations', JSON.stringify(registrations));
    }

    // Export report functionality
    exportReport.addEventListener('click', function() {
        // Get prediction data
        const prediction = getCurrentPredictionData();
        if (!prediction) {
            Swal.fire({
                icon: 'error',
                title: 'No Data Available',
                text: 'No prediction data available to export.',
                confirmButtonColor: '#27ae60'
            });
            return;
        }
        
        // Create PDF document
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();
        
        // Add header
        doc.setFontSize(22);
        doc.setTextColor(39, 174, 96);
        doc.text('Solar Generation Prediction Report', 105, 20, { align: 'center' });
        
        // Add date and location
        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        const selectedDate = new Date(document.getElementById('dateInput').value);
        const displayDate = selectedDate.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        doc.text(`Date: ${displayDate}`, 20, 35);
        doc.text(`Location: ${selectedLatLng.lat.toFixed(4)}, ${selectedLatLng.lng.toFixed(4)}`, 20, 45);
        
        // Add prediction summary
        doc.setFontSize(16);
        doc.text('Prediction Summary', 20, 60);
        
        doc.setFontSize(12);
        doc.text(`Total Energy Generated: ${prediction.total_energy} kWh`, 30, 70);
        
        // Add weather information if available
        if (prediction.weather) {
            doc.text('Weather Conditions:', 30, 80);
            doc.text(`Temperature: ${prediction.weather.temperature} °C`, 40, 90);
            doc.text(`Humidity: ${prediction.weather.humidity}%`, 40, 100);
            doc.text(`Solar Radiation: ${prediction.weather.radiation} W/m²`, 40, 110);
            doc.text(`Cloud Cover: ${prediction.weather.cloud_cover}%`, 40, 120);
        }
        
        // Add chart image if available
        if (window.dashboardEnergyChartInstance) {
            doc.text('Energy Production Forecast:', 20, 135);
            const chartCanvas = document.getElementById('dashboard-energy-chart');
            const chartImage = chartCanvas.toDataURL('image/png', 1.0);
            doc.addImage(chartImage, 'PNG', 20, 140, 170, 80);
        }
        
        // Add footer
        doc.setFontSize(10);
        doc.setTextColor(100, 100, 100);
        doc.text('Generated by SolarPredict - Advanced Solar Generation Prediction Platform', 105, 280, { align: 'center' });
        doc.text(`Report generated on ${new Date().toLocaleString()}`, 105, 285, { align: 'center' });
        
        // Save the PDF
        const filename = `Solar_Prediction_${selectedDate.toISOString().split('T')[0]}.pdf`;
        doc.save(filename);
        
        // Show success message
        Swal.fire({
            title: 'Report Generated',
            text: 'Your solar prediction report has been exported successfully.',
            icon: 'success',
            confirmButtonColor: '#27ae60'
        });
    });

    // Helper function to get current prediction data
    function getCurrentPredictionData() {
        // Check if we have prediction data in the page
        const totalEnergyElement = document.getElementById('dashboard-energy');
        if (!totalEnergyElement || totalEnergyElement.textContent === '-- kWh') {
            return null;
        }
        
        // Construct prediction object from displayed data
        const prediction = {
            total_energy: totalEnergyElement.textContent.replace(' kWh', ''),
            weather: {}
        };
        
        // Get weather data if available
        const tempElement = document.getElementById('dashboard-temp');
        if (tempElement) {
            prediction.weather.temperature = tempElement.textContent.replace(' °C', '');
        }
        
        const humidityElement = document.getElementById('dashboard-humidity');
        if (humidityElement) {
            prediction.weather.humidity = humidityElement.textContent.replace('%', '');
        }
        
        const radiationElement = document.getElementById('dashboard-radiation');
        if (radiationElement) {
            prediction.weather.radiation = radiationElement.textContent.replace(' W/m²', '');
        }
        
        const cloudElement = document.getElementById('dashboard-clouds');
        if (cloudElement) {
            prediction.weather.cloud_cover = cloudElement.textContent.replace('%', '');
        }
        
        return prediction;
    }

    // Enhanced confirm location and predict function
function confirmLocationAndPredict() {
    const date = document.getElementById('dateInput').value;
    if (!date || !selectedLatLng) {
        Swal.fire({
            icon: 'warning',
            title: 'Incomplete Selection',
            text: 'Please select a date and location first!',
            confirmButtonColor: '#27ae60'
        });
        return;
    }

    document.getElementById('loading').style.display = 'block';

    // Store the selected location for later use
    window.selectedLocation = {
        lat: selectedLatLng.lat,
        lng: selectedLatLng.lng
    };

    const formData = new FormData();
    formData.append('lat', selectedLatLng.lat);
    formData.append('lon', selectedLatLng.lng);
    formData.append('date', date);
    formData.append('latestWeatherData', JSON.stringify(latestWeatherData));

    fetch('/confirm_location_and_predict/', {
        method: 'POST',
        body: formData,
        headers: { "X-CSRFToken": getCookie('csrftoken') },
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        document.getElementById('loading').style.display = 'none';
        
        if (data.error) {
            Swal.fire({
                icon: 'error',
                title: 'Prediction Error',
                text: data.error,
                confirmButtonColor: '#27ae60'
            });
            return;
        }
        
        if (data.status === 'success' && data.prediction) {
            // Update the prediction data with location information
            data.prediction.location = {
                lat: selectedLatLng.lat,
                lng: selectedLatLng.lng
            };
            
            // Display the prediction results
            displayPredictionResults(data.prediction);
            
            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Prediction Complete',
                text: 'Solar generation prediction has been successfully generated!',
                confirmButtonColor: '#27ae60'
            });
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'Incomplete Results',
                text: 'The prediction was processed but returned incomplete results.',
                confirmButtonColor: '#27ae60'
            });
        }
    })
    .catch(error => {
        document.getElementById('loading').style.display = 'none';
        console.error('Prediction error:', error);
        
        Swal.fire({
            icon: 'error',
            title: 'Prediction Failed',
            text: `An error occurred: ${error.message}`,
            confirmButtonColor: '#27ae60'
        });
    });
}

    // Create hourly forecast display
    function createHourlyForecast() {
        const hourlyContainer = document.getElementById('hourly-forecast');
        hourlyContainer.innerHTML = '';
        
        // Get current hour
        const currentHour = new Date().getHours();
        
        // Create 24 hour forecast
        for (let i = 0; i < 24; i++) {
            const hour = (currentHour + i) % 24;
            const temp = Math.round(15 + Math.random() * 10); // Random temp between 15-25°C
            const clouds = Math.round(Math.random() * 100); // Random cloud cover
            
            let weatherIcon = 'fa-sun';
            let weatherColor = '#f39c12';
            
            if (clouds > 80) {
                weatherIcon = 'fa-cloud';
                weatherColor = '#7f8c8d';
            } else if (clouds > 50) {
                weatherIcon = 'fa-cloud-sun';
                weatherColor = '#95a5a6';
            }
            
            const hourlyItem = document.createElement('div');
            hourlyItem.className = 'hourly-item';
            hourlyItem.innerHTML = `
                <p>${hour}:00</p>
                <i class="fas ${weatherIcon}" style="color: ${weatherColor};"></i>
                <p class="temp">${temp}°C</p>
                <p class="small">${clouds}% ☁️</p>
            `;
            
            hourlyContainer.appendChild(hourlyItem);
        }
    }
    
    // Create dashboard energy chart
    function createDashboardEnergyChart(data) {
        console.log("Creating dashboard energy chart with data:", data);
        
        const ctxContainer = document.getElementById('dashboard-energy-chart');
        if (!ctxContainer) {
            console.error("Cannot find dashboard-energy-chart element");
            return;
        }
        
        // Clear any existing chart
        if (window.dashboardEnergyChartInstance) {
            window.dashboardEnergyChartInstance.destroy();
        }
        
        // Check if we have the expected data structure
        if (!data.hours || !data.predicted_energy || !data.GHI) {
            console.error("Missing required data for energy chart:", data);
            ctxContainer.innerHTML = '<div class="text-center text-danger">Invalid data format for energy chart</div>';
            return;
        }
        
        // Create formatted datetime labels that include both date and time
        const times = [];
        if (data.hours && data.hours.length) {
            // Get the selected date
            const selectedDate = document.getElementById('dateInput').value;
            
            // Create datetime strings for each hour
            data.hours.forEach((timeStr, index) => {
                // For simplicity, assume each consecutive 24 hours is a new day
                const dayOffset = Math.floor(index / 24);
                const date = new Date(selectedDate);
                date.setDate(date.getDate() + dayOffset);
                
                // Format as YYYY-MM-DD HH:MM
                const dateStr = date.toISOString().split('T')[0];
                times.push(`${dateStr} ${timeStr}`);
            });
        } else {
            console.warn("No valid hours data found.");
        }
        
        // Filter data to only show the next 3 days (72 hours)
        const maxHours = 72;
        const hoursToShow = Math.min(data.hours.length, maxHours);
        
        const filteredTimes = times.slice(0, hoursToShow);
        const filteredEnergy = data.predicted_energy.slice(0, hoursToShow);
        const filteredGHI = data.GHI.slice(0, hoursToShow);
        
        // Add zoom controls to the container
        ctxContainer.parentElement.innerHTML = `
            <div class="chart-controls mb-3 d-flex align-items-center">
                <div class="zoom-help me-auto">
                    <small class="text-muted"><i class="fas fa-info-circle me-1"></i>Use mouse wheel to zoom in/out</small>
                </div>
                <div class="btn-group">
                    <button id="energy-zoom-in" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button id="energy-zoom-out" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button id="energy-reset-zoom" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
            </div>
            <div class="chart-container" style="position: relative; height: 400px; background-color: rgba(255, 255, 255, 0.8); border-radius: 10px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); padding: 15px;">
                <canvas id="dashboard-energy-chart"></canvas>
            </div>
        `;
        
        // Create the chart with filtered data
        window.dashboardEnergyChartInstance = new Chart(document.getElementById('dashboard-energy-chart'), {
            type: 'line',
            data: {
                labels: filteredTimes,
                datasets: [
                    {
                        label: 'Energy Production (kWh)',
                        data: filteredEnergy,
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHoverBackgroundColor: '#27ae60',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2
                    },
                    {
                        label: 'Solar Radiation (W/m²)',
                        data: filteredGHI,
                        borderColor: '#f39c12',
                        borderDash: [5, 5],
                        fill: false,
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHoverBackgroundColor: '#f39c12',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                plugins: { 
                    legend: { 
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 10,
                            font: {
                                size: 12
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '3-Day Energy Production Forecast',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 15
                        }
                    },
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'x',
                            threshold: 5
                        },
                        zoom: {
                            wheel: { 
                                enabled: true,
                                speed: 0.05
                            },
                            pinch: { 
                                enabled: true 
                            },
                            mode: 'x' // Only zoom on x-axis (time)
                        },
                        limits: {
                            x: {
                                min: 'original', // Don't allow zooming beyond the original data range
                                max: 'original',
                                minRange: 4 // Minimum 4 time points visible
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        cornerRadius: 6,
                        padding: 10,
                        boxPadding: 5,
                        usePointStyle: true,
                        callbacks: {
                            title: function(tooltipItems) {
                                const parts = tooltipItems[0].label.split(' ');
                                const date = parts[0];
                                const time = parts[1];
                                return `${date} at ${time}`;
                            },
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y.toFixed(2);
                                    if (context.dataset.label === 'Energy Production (kWh)') {
                                        label += ' kWh';
                                    } else if (context.dataset.label === 'Solar Radiation (W/m²)') {
                                        label += ' W/m²';
                                    }
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    y: { 
                        beginAtZero: true, 
                        title: { 
                            display: true, 
                            text: 'Energy (kWh)',
                            font: {
                                weight: 'bold',
                                size: 12
                            },
                            padding: {top: 0, bottom: 10}
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 8,
                            font: {
                                size: 11
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        grid: { 
                            drawOnChartArea: false,
                            drawBorder: false
                        },
                        title: { 
                            display: true, 
                            text: 'Solar Radiation (W/m²)',
                            font: {
                                weight: 'bold',
                                size: 12
                            },
                            padding: {top: 0, bottom: 10}
                        },
                        ticks: {
                            padding: 8,
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: { 
                        title: { 
                            display: true, 
                            text: 'Date/Time',
                            font: {
                                weight: 'bold',
                                size: 12
                            },
                            padding: {top: 10, bottom: 0}
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: { 
                            maxRotation: 0,
                            autoSkip: true,
                            padding: 8,
                            font: {
                                size: 11
                            },
                            callback: function(value, index, values) {
                                const timeStr = this.getLabelForValue(value);
                                const parts = timeStr.split(' ');
                                if (parts.length < 2) return timeStr;
                                
                                const timePart = parts[1];
                                const datePart = parts[0];
                                const hour = parseInt(timePart.split(':')[0]);
                                
                                // Calculate visible range
                                const visibleDataPoints = values.length;
                                
                                // If zoomed in (less than 24 data points visible), show more time details
                                if (visibleDataPoints < 24) {
                                    // Show time every 2 hours
                                    if (hour % 2 === 0) {
                                        return hour === 0 ? `${datePart}` : `${hour}:00`;
                                    }
                                    return '';
                                } else if (visibleDataPoints < 48) {
                                    // Medium zoom level - show only 00:00, 06:00, 12:00, 18:00
                                    if (hour % 6 === 0) {
                                        return hour === 0 ? `${datePart}` : `${hour}:00`;
                                    }
                                    return '';
                                } else {
                                    // When zoomed out, only show dates at midnight
                                    return hour === 0 ? datePart : '';
                                }
                            }
                        }
                    }
                }
            }
        });
        
        // Add event listeners for zoom control buttons with smoother zooming
        document.getElementById('energy-zoom-in').addEventListener('click', function() {
            window.dashboardEnergyChartInstance.zoom(1.1);
        });

        document.getElementById('energy-zoom-out').addEventListener('click', function() {
            window.dashboardEnergyChartInstance.zoom(0.9);
        });

        document.getElementById('energy-reset-zoom').addEventListener('click', function() {
            window.dashboardEnergyChartInstance.resetZoom();
        });

        // Add a visual indicator when zooming
        const chartCanvas = document.getElementById('dashboard-energy-chart');
        chartCanvas.addEventListener('wheel', function(e) {
            if (e.deltaY < 0) {
                chartCanvas.style.cursor = 'zoom-in';
            } else {
                chartCanvas.style.cursor = 'zoom-out';
            }
            
            // Reset cursor after a short delay
            setTimeout(() => {
                chartCanvas.style.cursor = 'default';
            }, 300);
        });
    }

    // Create dashboard weekly chart
    function createDashboardWeeklyChart(data) {
        console.log("Creating dashboard weekly chart with data:", data);
        
        const ctxContainer = document.getElementById('dashboard-weekly-chart');
        if (!ctxContainer) {
            console.error("Cannot find dashboard-weekly-chart element");
            return;
        }
        
        // Clear any existing chart
        if (window.dashboardWeeklyChartInstance) {
            window.dashboardWeeklyChartInstance.destroy();
        }
        
        // Check if we have the expected data structure
        if (!data.dates || !data.predicted_energy_kWh) {
            console.error("Missing required data for weekly chart:", data);
            ctxContainer.innerHTML = '<div class="text-center text-danger">Invalid data format for weekly chart</div>';
            return;
        }
        
        // Create the chart
        window.dashboardWeeklyChartInstance = new Chart(ctxContainer, {
            type: 'bar',
            data: {
                labels: data.dates,
                datasets: [
                    {
                        label: 'Daily Energy Production (kWh)',
                        data: data.predicted_energy_kWh,
                        backgroundColor: 'rgba(39, 174, 96, 0.7)',
                        borderColor: '#27ae60',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'top' }},
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Energy (kWh)' }},
                    x: { title: { display: true, text: 'Date' }}
                }
            }
        });
    }

// Add this helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>

<!-- SweetAlert2 for better alerts -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock scripts %}
